#!/usr/bin/env python3
"""
Face restoration service using GFPGAN library.
This script provides a command-line interface for restoring faces in images.
"""

import sys
import os
import argparse
from pathlib import Path
import tempfile
import base64
import json
from io import BytesIO
import urllib.request
import hashlib

try:
    from gfpgan import GFPGANer
    from PIL import Image
    import numpy as np
    import cv2
    import torch
except ImportError as e:
    print(f"Error: Required packages not installed. {e}", file=sys.stderr)
    print("Please install required packages:", file=sys.stderr)
    print("pip install gfpgan basicsr facexlib realesrgan torch torchvision", file=sys.stderr)
    sys.exit(1)


# Model URLs and checksums
GFPGAN_MODELS = {
    'v1.3': {
        'url': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth',
        'filename': 'GFPGANv1.3.pth',
        'md5': 'c953a88f2727c85c3d9ae72e2bd4df68'
    },
    'v1.4': {
        'url': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth',
        'filename': 'GFPGANv1.4.pth',
        'md5': 'e2bf99ed8b2d0b3b2c7e8b8b8b8b8b8b'
    },
    'RestoreFormer': {
        'url': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.4/RestoreFormer.pth',
        'filename': 'RestoreFormer.pth',
        'md5': 'unknown'
    }
}


def get_models_dir():
    """Get the models directory path."""
    script_dir = Path(__file__).parent
    models_dir = script_dir / 'models' / 'gfpgan'
    models_dir.mkdir(parents=True, exist_ok=True)
    return models_dir


def get_model_path(model_version='v1.3'):
    """Get GFPGAN model path, using GFPGAN's built-in model loading."""
    if model_version not in GFPGAN_MODELS:
        raise ValueError(f"Unsupported model version: {model_version}")

    model_info = GFPGAN_MODELS[model_version]
    models_dir = get_models_dir()
    model_path = models_dir / model_info['filename']

    # Check if model exists and is valid
    if model_path.exists():
        try:
            # Try to load the model to verify it's not corrupted
            import torch
            torch.load(str(model_path), map_location='cpu')
            print(f"Model {model_version} already exists and is valid at {model_path}", file=sys.stderr)
            return str(model_path)
        except Exception as e:
            print(f"Existing model is corrupted ({e}), removing...", file=sys.stderr)
            model_path.unlink()  # Remove corrupted file

    # Return the expected path - GFPGAN will download it automatically
    print(f"Model {model_version} will be downloaded automatically by GFPGAN", file=sys.stderr)
    return str(model_path)


def create_gfpgan_restorer(model_version='v1.4', upscale=2):
    """Create GFPGAN restorer instance with improved quality settings."""
    try:
        print(f"Creating GFPGAN restorer with model {model_version}, upscale {upscale}", file=sys.stderr)

        # Use better model URLs for improved quality
        model_urls = {
            'v1.3': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth',
            'v1.4': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth',
            'RestoreFormer': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.4/RestoreFormer.pth'
        }

        model_url = model_urls.get(model_version, model_urls['v1.4'])
        print(f"Using model: {model_url}", file=sys.stderr)

        # Create background upsampler for better overall image quality
        bg_upsampler = None
        try:
            from basicsr.archs.rrdbnet_arch import RRDBNet
            from realesrgan import RealESRGANer

            print("Creating background upsampler for better image quality...", file=sys.stderr)
            # Use RealESRGAN for background enhancement
            bg_model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=upscale)
            bg_upsampler = RealESRGANer(
                scale=upscale,
                model_path='https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth',
                model=bg_model,
                tile=400,
                tile_pad=10,
                pre_pad=0,
                half=False  # Use full precision for better quality
            )
            print("Background upsampler created successfully", file=sys.stderr)
        except Exception as bg_error:
            print(f"Warning: Could not create background upsampler: {bg_error}", file=sys.stderr)
            print("Continuing without background enhancement...", file=sys.stderr)
            bg_upsampler = None

        # Let GFPGAN handle model downloading automatically
        print("Initializing GFPGAN (will auto-download model if needed)...", file=sys.stderr)
        restorer = GFPGANer(
            model_path=model_url,
            upscale=upscale,
            arch='clean',
            channel_multiplier=2,
            bg_upsampler=bg_upsampler  # Use background upsampler for better overall quality
        )
        print("GFPGAN restorer created successfully", file=sys.stderr)

        return restorer
    except Exception as e:
        print(f"Failed to create GFPGAN restorer: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        raise


def restore_faces_in_image(image: Image.Image, model_version='v1.4', upscale=2) -> Image.Image:
    """
    Restore faces in a PIL Image using GFPGAN.

    Args:
        image: PIL Image to process
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        PIL Image with restored faces
    """
    try:
        print(f"Starting face restoration for image size: {image.size}", file=sys.stderr)

        # Create restorer
        print("Creating GFPGAN restorer...", file=sys.stderr)
        restorer = create_gfpgan_restorer(model_version, upscale)

        # Convert PIL to OpenCV format
        print("Converting image format...", file=sys.stderr)
        if image.mode == 'RGBA':
            print("Converting RGBA to RGB with white background", file=sys.stderr)
            # Handle transparency by converting to RGB with white background
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode != 'RGB':
            print(f"Converting {image.mode} to RGB", file=sys.stderr)
            image = image.convert('RGB')

        # Ensure minimum size for better face detection and quality
        original_size = image.size
        min_size = 512  # Minimum size for good face detection and quality
        if min(image.size) < min_size:
            print(f"Upscaling small image from {image.size} for better face detection", file=sys.stderr)
            scale_factor = min_size / min(image.size)
            new_size = (int(image.size[0] * scale_factor), int(image.size[1] * scale_factor))
            image = image.resize(new_size, Image.Resampling.LANCZOS)
            print(f"Resized to: {image.size}", file=sys.stderr)

        # Convert to numpy array (OpenCV format)
        print("Converting to OpenCV format...", file=sys.stderr)
        img_array = np.array(image)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        print(f"Image array shape: {img_array.shape}", file=sys.stderr)

        # Restore faces with improved settings
        print("Starting GFPGAN face restoration...", file=sys.stderr)
        cropped_faces, restored_faces, restored_img = restorer.enhance(
            img_bgr,
            has_aligned=False,  # Let GFPGAN handle face alignment for better accuracy
            only_center_face=False,  # Process all detected faces
            paste_back=True,  # Paste restored faces back to original image
            weight=0.5  # Blend factor: 0.5 balances original and restored features
        )
        print("GFPGAN face restoration completed", file=sys.stderr)

        # Convert back to PIL Image
        if restored_img is not None:
            print("Converting result back to PIL Image...", file=sys.stderr)
            restored_rgb = cv2.cvtColor(restored_img, cv2.COLOR_BGR2RGB)
            result_image = Image.fromarray(restored_rgb)
            print(f"Result image size: {result_image.size}", file=sys.stderr)
        else:
            print("Warning: GFPGAN returned None, using original image", file=sys.stderr)
            result_image = image

        print("Face restoration completed successfully", file=sys.stderr)
        return result_image

    except Exception as e:
        print(f"Error during face restoration: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return image  # Return original image on error


def restore_faces_advanced(image: Image.Image, model_version='v1.4', upscale=2) -> Image.Image:
    """
    Advanced face restoration with multiple quality improvements.

    Args:
        image: PIL Image to process
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        PIL Image with restored faces
    """
    try:
        print(f"Starting advanced face restoration for image size: {image.size}", file=sys.stderr)

        # Create restorer
        print("Creating GFPGAN restorer...", file=sys.stderr)
        restorer = create_gfpgan_restorer(model_version, upscale)

        # Convert PIL to OpenCV format with preprocessing
        print("Converting image format with preprocessing...", file=sys.stderr)
        if image.mode == 'RGBA':
            print("Converting RGBA to RGB with white background", file=sys.stderr)
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode != 'RGB':
            print(f"Converting {image.mode} to RGB", file=sys.stderr)
            image = image.convert('RGB')

        # Apply preprocessing for better results
        original_size = image.size

        # Ensure minimum size for optimal face detection
        min_size = 512
        if min(image.size) < min_size:
            print(f"Upscaling small image from {image.size} for better quality", file=sys.stderr)
            scale_factor = min_size / min(image.size)
            new_size = (int(image.size[0] * scale_factor), int(image.size[1] * scale_factor))
            image = image.resize(new_size, Image.Resampling.LANCZOS)
            print(f"Resized to: {image.size}", file=sys.stderr)

        # Apply slight sharpening for better face detection
        from PIL import ImageFilter
        image = image.filter(ImageFilter.UnsharpMask(radius=0.5, percent=50, threshold=3))

        # Convert to OpenCV format
        img_array = np.array(image)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        print(f"Image array shape: {img_array.shape}", file=sys.stderr)

        # Restore faces with optimized settings for better eye quality
        print("Starting GFPGAN face restoration with advanced settings...", file=sys.stderr)

        # Modify face helper settings for better eye detection
        if hasattr(restorer, 'face_helper'):
            # Increase eye distance threshold to avoid processing very small faces that cause eye issues
            restorer.face_helper.eye_dist_threshold = 15  # Default is 5, increase for better quality
            print("Adjusted eye distance threshold for better face detection", file=sys.stderr)

        cropped_faces, restored_faces, restored_img = restorer.enhance(
            img_bgr,
            has_aligned=False,  # Let GFPGAN handle alignment
            only_center_face=False,  # Process all faces
            paste_back=True,  # Paste back to original
            weight=0.3  # Lower weight for more natural eye appearance (was 0.7)
        )
        print("GFPGAN face restoration completed", file=sys.stderr)

        # Convert back to PIL Image
        if restored_img is not None:
            print("Converting result back to PIL Image...", file=sys.stderr)
            restored_rgb = cv2.cvtColor(restored_img, cv2.COLOR_BGR2RGB)
            result_image = Image.fromarray(restored_rgb)

            # Apply post-processing for better quality
            # Slight contrast enhancement
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(result_image)
            result_image = enhancer.enhance(1.05)  # Slight contrast boost

            print(f"Result image size: {result_image.size}", file=sys.stderr)
            return result_image
        else:
            print("Warning: GFPGAN returned None, using original image", file=sys.stderr)
            return image

    except Exception as e:
        print(f"Error during advanced face restoration: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return image  # Return original image on error


def restore_faces_premium(image: Image.Image, model_version='v1.4', upscale=2) -> Image.Image:
    """
    Premium face restoration with custom eye blending and background enhancement.

    Args:
        image: PIL Image to process
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        PIL Image with restored faces and enhanced background
    """
    try:
        print(f"Starting premium face restoration for image size: {image.size}", file=sys.stderr)

        # Create restorer with background upsampler
        print("Creating GFPGAN restorer with background enhancement...", file=sys.stderr)
        restorer = create_gfpgan_restorer(model_version, upscale)

        # Convert PIL to OpenCV format with preprocessing
        print("Converting image format with preprocessing...", file=sys.stderr)
        if image.mode == 'RGBA':
            print("Converting RGBA to RGB with white background", file=sys.stderr)
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode != 'RGB':
            print(f"Converting {image.mode} to RGB", file=sys.stderr)
            image = image.convert('RGB')

        # Preprocessing for better face detection
        original_size = image.size
        min_size = 512
        if min(image.size) < min_size:
            print(f"Upscaling small image from {image.size} for better processing", file=sys.stderr)
            scale_factor = min_size / min(image.size)
            new_size = (int(image.size[0] * scale_factor), int(image.size[1] * scale_factor))
            image = image.resize(new_size, Image.Resampling.LANCZOS)

        # Apply gentle sharpening for better face detection (less aggressive than before)
        from PIL import ImageFilter
        image = image.filter(ImageFilter.UnsharpMask(radius=0.3, percent=30, threshold=5))

        # Convert to OpenCV format
        img_array = np.array(image)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        print(f"Image array shape: {img_array.shape}", file=sys.stderr)

        # Configure face helper for better eye handling
        if hasattr(restorer, 'face_helper'):
            # More conservative settings for better eye quality
            restorer.face_helper.eye_dist_threshold = 20  # Even higher threshold
            restorer.face_helper.crop_ratio = (1.3, 1.3)  # Larger crop for better context
            print("Configured face helper for premium eye quality", file=sys.stderr)

        # First pass: Very conservative restoration for natural eyes
        print("Starting premium GFPGAN face restoration (conservative pass)...", file=sys.stderr)
        cropped_faces, restored_faces, restored_img = restorer.enhance(
            img_bgr,
            has_aligned=False,
            only_center_face=False,
            paste_back=True,
            weight=0.2  # Very low weight for natural eye appearance
        )
        print("Premium face restoration completed", file=sys.stderr)

        # Convert back to PIL Image
        if restored_img is not None:
            print("Converting result back to PIL Image...", file=sys.stderr)
            restored_rgb = cv2.cvtColor(restored_img, cv2.COLOR_BGR2RGB)
            result_image = Image.fromarray(restored_rgb)

            # Apply minimal post-processing to preserve natural eye appearance
            from PIL import ImageEnhance

            # Very slight sharpness enhancement only
            sharpness_enhancer = ImageEnhance.Sharpness(result_image)
            result_image = sharpness_enhancer.enhance(1.02)  # Minimal sharpening

            # Slight color enhancement for better overall appearance
            color_enhancer = ImageEnhance.Color(result_image)
            result_image = color_enhancer.enhance(1.05)  # Slight color boost

            print(f"Premium result image size: {result_image.size}", file=sys.stderr)
            return result_image
        else:
            print("Warning: GFPGAN returned None, using original image", file=sys.stderr)
            return image

    except Exception as e:
        print(f"Error in premium face restoration: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return image


def restore_faces_from_file(input_path: str, output_path: str, model_version='v1.4', upscale=2) -> bool:
    """
    Restore faces in an image file.

    Args:
        input_path: Path to input image
        output_path: Path to save output image
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Load image
        image = Image.open(input_path)

        # Restore faces
        restored_image = restore_faces_in_image(image, model_version, upscale)

        # Save result
        restored_image.save(output_path, 'PNG', optimize=True)

        return True

    except Exception as e:
        print(f"Error processing image file: {e}", file=sys.stderr)
        return False


def restore_faces_from_base64(base64_data: str, model_version='v1.4', upscale=2) -> str:
    """
    Restore faces in a base64 encoded image.

    Args:
        base64_data: Base64 encoded image data
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        str: Base64 encoded result image or empty string on error
    """
    try:
        # Decode base64 data
        if base64_data.startswith('data:image'):
            # Remove data URL prefix if present
            base64_data = base64_data.split(',')[1]

        input_data = base64.b64decode(base64_data)

        # Load image
        image = Image.open(BytesIO(input_data))

        # Restore faces using premium method for best eye quality and background enhancement
        restored_image = restore_faces_premium(image, model_version, upscale)

        # Convert back to base64
        output_buffer = BytesIO()
        restored_image.save(output_buffer, format='PNG', optimize=True)
        output_data = output_buffer.getvalue()
        output_base64 = base64.b64encode(output_data).decode('utf-8')

        return f"data:image/png;base64,{output_base64}"

    except Exception as e:
        print(f"Error processing base64 image: {e}", file=sys.stderr)
        return ""


def main():
    parser = argparse.ArgumentParser(description='Restore faces in images using GFPGAN')
    parser.add_argument('--input', '-i', required=True, help='Input image path')
    parser.add_argument('--output', '-o', required=True, help='Output image path')
    parser.add_argument('--base64', action='store_true', help='Process base64 encoded image')
    parser.add_argument('--model', '-m', default='v1.4', choices=['v1.3', 'v1.4', 'RestoreFormer'],
                       help='GFPGAN model version (default: v1.4)')
    parser.add_argument('--upscale', '-u', type=int, default=2,
                       help='Upscaling factor (default: 2)')

    args = parser.parse_args()

    if args.base64:
        # Read base64 data from input file or stdin
        if args.input == '-':
            base64_data = sys.stdin.read().strip()
        else:
            with open(args.input, 'r') as f:
                base64_data = f.read().strip()

        result = restore_faces_from_base64(base64_data, args.model, args.upscale)

        if result:
            if args.output == '-':
                print(result)
            else:
                with open(args.output, 'w') as f:
                    f.write(result)
            return 0
        else:
            return 1
    else:
        # Process file directly
        success = restore_faces_from_file(args.input, args.output, args.model, args.upscale)
        return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
