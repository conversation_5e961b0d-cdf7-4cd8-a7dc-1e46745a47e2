Metadata-Version: 2.4
Name: neuralgym
Version: 0.0.1
Summary: Deep Learning Toolkit
Home-page: https://github.com/JiahuiYu/neuralgym
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: deeplearning toolkit
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: summary

NeuralGym: Deep Learning Toolkit
=================================

NeuralGym is born for fast prototyping of experimental ideas on deep learning.

[![ReadTheDoc](https://readthedocs.org/projects/neuralgym/badge/?version=latest)](http://neuralgym.readthedocs.io)

Due to rapid/messy development and stability concerns, currently only limited modules are released for reproducing other repositories.

## Document

[ReadTheDocs: NeuralGym](http://neuralgym.readthedocs.io)

## Installation

`pip install git+https://github.com/JiahuiYu/neuralgym`
