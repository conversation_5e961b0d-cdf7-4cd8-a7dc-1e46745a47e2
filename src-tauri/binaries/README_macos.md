# RealESRGAN

We have provided the following models:

1. realesr-animevideov3 (default)
2. realesrgan-x4plus
3. realesrgan-x4plus-anime

Command:

You may need to change file mode before running.

chmod u+x realesrgan-ncnn-vulkan

1. ./realesrgan-ncnn-vulkan -i input.jpg -o output.png
2. ./realesrgan-ncnn-vulkan -i input.jpg -o output.png -n realesr-animevideov3
3. ./realesrgan-ncnn-vulkan -i input_folder -o outputfolder -n realesr-animevideov3 -s 2 -f jpg
4. ./realesrgan-ncnn-vulkan -i input_folder -o outputfolder -n realesr-animevideov3 -s 4 -f jpg

Commands for enhancing anime videos:

1. Use ffmpeg to extract frames from a video (Remember to create the folder `tmp_frames` ahead)

    ffmpeg -i onepiece_demo.mp4 -qscale:v 1 -qmin 1 -qmax 1 -vsync 0 tmp_frames/frame%08d.jpg

2. Inference with Real-ESRGAN executable file (Remember to create the folder `out_frames` ahead)

    ./realesrgan-ncnn-vulkan -i tmp_frames -o out_frames -n realesr-animevideov3 -s 2 -f jpg

3. Merge the enhanced frames back into a video

    ffmpeg -i out_frames/frame%08d.jpg -i onepiece_demo.mp4 -map 0:v:0 -map 1:a:0 -c:a copy -c:v libx264 -r 23.98 -pix_fmt yuv420p output_w_audio.mp4


If you encounter the problem that *macOS cannot verify that this app is free from Malware*, you have two options to try:

1. Choose *Apple menu* > go to *System Preferences* > choose *Security & Privacy* > tap the General tab > click the *Open Anyway* button.
2. OR run command:  `sudo spctl --master-disable`  to disable Gatekeeper.

------------------------

GitHub: https://github.com/xinntao/Real-ESRGAN/
Paper: https://arxiv.org/abs/2107.10833

------------------------

This executable file is **portable** and includes all the binaries and models required. No CUDA or PyTorch environment is needed.

Note that it may introduce block inconsistency (and also generate slightly different results from the PyTorch implementation), because this executable file first crops the input image into several tiles, and then processes them separately, finally stitches together.

This executable file is based on the wonderful [Tencent/ncnn](https://github.com/Tencent/ncnn) and [realsr-ncnn-vulkan](https://github.com/nihui/realsr-ncnn-vulkan) by [nihui](https://github.com/nihui).
