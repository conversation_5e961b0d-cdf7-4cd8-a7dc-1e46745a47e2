#!/usr/bin/env python3
"""
Simple test script for GFPGAN face restoration to debug hanging issues.
This script tests face restoration with a small test image.
"""

import sys
import os
from pathlib import Path
import base64
from PIL import Image
import io

# Add python-backend to path
backend_dir = Path(__file__).parent / "python-backend"
sys.path.insert(0, str(backend_dir))

def create_test_image():
    """Create a simple test image with a face-like pattern."""
    # Create a simple 256x256 RGB image with a basic face pattern
    img = Image.new('RGB', (256, 256), color='white')
    
    # Add some basic shapes to simulate a face
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # Face outline (circle)
    draw.ellipse([50, 50, 206, 206], outline='black', width=2)
    
    # Eyes
    draw.ellipse([80, 100, 100, 120], fill='black')
    draw.ellipse([156, 100, 176, 120], fill='black')
    
    # Nose
    draw.line([128, 130, 128, 150], fill='black', width=2)
    
    # Mouth
    draw.arc([100, 160, 156, 180], start=0, end=180, fill='black', width=2)
    
    return img

def test_face_restoration():
    """Test face restoration with a simple image."""
    print("🧪 Testing GFPGAN face restoration...")
    
    try:
        # Import face restoration module
        import face_restoration
        print("✅ Face restoration module imported successfully")
        
        # Create test image
        print("📷 Creating test image...")
        test_img = create_test_image()
        print(f"✅ Test image created: {test_img.size}")
        
        # Test face restoration
        print("🔄 Starting face restoration...")
        print("   This may take a few minutes on first run (model download)")
        
        restored_img = face_restoration.restore_faces_in_image(
            test_img, 
            model_version='v1.3', 
            upscale=2
        )
        
        print(f"✅ Face restoration completed: {restored_img.size}")
        
        # Save result for inspection
        output_path = Path("test_face_restoration_result.png")
        restored_img.save(output_path)
        print(f"💾 Result saved to: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Face restoration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base64_processing():
    """Test base64 processing (same as app uses)."""
    print("\n🧪 Testing base64 processing...")
    
    try:
        # Import face restoration module
        import face_restoration
        
        # Create test image
        test_img = create_test_image()
        
        # Convert to base64 (same as app)
        buffer = io.BytesIO()
        test_img.save(buffer, format='PNG')
        img_data = buffer.getvalue()
        base64_data = base64.b64encode(img_data).decode('utf-8')
        base64_with_prefix = f"data:image/png;base64,{base64_data}"
        
        print(f"📷 Test image converted to base64: {len(base64_with_prefix)} chars")
        
        # Test base64 restoration
        print("🔄 Starting base64 face restoration...")
        result_base64 = face_restoration.restore_faces_from_base64(
            base64_with_prefix,
            model_version='v1.3',
            upscale=2
        )
        
        if result_base64:
            print(f"✅ Base64 face restoration completed: {len(result_base64)} chars")
            
            # Convert back to image and save
            result_data = base64.b64decode(result_base64.split(',')[1])
            result_img = Image.open(io.BytesIO(result_data))
            
            output_path = Path("test_face_restoration_base64_result.png")
            result_img.save(output_path)
            print(f"💾 Base64 result saved to: {output_path}")
            
            return True
        else:
            print("❌ Base64 face restoration returned empty result")
            return False
            
    except Exception as e:
        print(f"❌ Base64 face restoration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run face restoration tests."""
    print("🚀 GFPGAN Face Restoration Debug Test")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("package.json").exists():
        print("❌ Error: Please run this script from the project root directory")
        return 1
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment may not be activated")
        print("   Please run: cd python-backend && source venv/bin/activate")
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Direct face restoration
    if test_face_restoration():
        tests_passed += 1
    
    # Test 2: Base64 processing (same as app)
    if test_base64_processing():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Face restoration is working correctly.")
        print("\n💡 If the app is still hanging:")
        print("   1. Check the browser console for errors")
        print("   2. Check the Tauri console for Python script output")
        print("   3. Try with a smaller image first")
        print("   4. Ensure sufficient RAM (4GB+ recommended)")
        return 0
    else:
        print("❌ Some tests failed. Face restoration may have issues.")
        print("\n🔧 Troubleshooting:")
        print("   1. Run: ./fix-gfpgan-dependencies.sh")
        print("   2. Check internet connection (for model download)")
        print("   3. Ensure sufficient disk space (2GB+ free)")
        print("   4. Check RAM usage (close other applications)")
        return 1

if __name__ == "__main__":
    sys.exit(main())
