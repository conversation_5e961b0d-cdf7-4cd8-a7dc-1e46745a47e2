#!/bin/bash

# Fix corrupted GFPGAN model files
# This script removes corrupted model files and allows them to be re-downloaded

echo "🔧 Fixing corrupted GFPGAN model files..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Navigate to python-backend directory
cd python-backend

echo "📁 Working in python-backend directory..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Error: Virtual environment not found. Please run setup-python-backend.sh first"
    exit 1
fi

# Check for corrupted model files
echo "🔍 Checking for corrupted model files..."

# Remove corrupted GFPGAN model files
if [ -d "models/gfpgan" ]; then
    echo "🗑️  Removing potentially corrupted GFPGAN model files..."
    rm -rf models/gfpgan/
    echo "✅ Removed corrupted model files"
else
    echo "ℹ️  No existing GFPGAN model directory found"
fi

# Remove corrupted weights directory
if [ -d "gfpgan/weights" ]; then
    echo "🗑️  Removing potentially corrupted weights directory..."
    rm -rf gfpgan/
    echo "✅ Removed corrupted weights directory"
else
    echo "ℹ️  No existing weights directory found"
fi

echo "✅ Cleanup completed!"
echo ""
echo "💡 Next steps:"
echo "   1. The models will be automatically re-downloaded on next use"
echo "   2. Start the app: npm run tauri dev"
echo "   3. Try the face restoration feature again"
echo "   4. Be patient during first use - models will download (~350MB total)"
echo ""
echo "🚀 Models will be downloaded fresh and should work correctly now!"
