#!/bin/bash

# Setup script for Upscayl integration
echo "Setting up Upscayl integration..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Create binaries directory if it doesn't exist
mkdir -p src-tauri/binaries

# Detect target architecture
TARGET_ARCH="aarch64-apple-darwin"
if [[ $(uname -m) == "x86_64" ]]; then
    TARGET_ARCH="x86_64-apple-darwin"
fi

# Check if upscayl-ncnn binary exists
if [ ! -f "src-tauri/binaries/upscayl-ncnn-${TARGET_ARCH}" ]; then
    echo "Downloading upscayl-ncnn binary for macOS..."
    cd src-tauri/binaries

    # Download Real-ESRGAN-ncnn-vulkan for macOS
    curl -L -o realesrgan-ncnn-vulkan-macos.zip "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.5.0/realesrgan-ncnn-vulkan-20220424-macos.zip"

    if [ $? -eq 0 ]; then
        echo "Download successful, extracting..."
        unzip realesrgan-ncnn-vulkan-macos.zip

        # Rename binary to match Tauri configuration
        mv realesrgan-ncnn-vulkan upscayl-ncnn-${TARGET_ARCH}
        chmod +x upscayl-ncnn-${TARGET_ARCH}

        # Clean up
        rm realesrgan-ncnn-vulkan-macos.zip
        rm -f input.jpg input2.jpg onepiece_demo.mp4 README_macos.md

        echo "✅ upscayl-ncnn binary installed successfully"
        echo "📁 Models available in src-tauri/binaries/models/"
        ls -la models/
    else
        echo "❌ Failed to download upscayl-ncnn binary"
        exit 1
    fi

    cd ../..
else
    echo "✅ upscayl-ncnn binary already exists"
fi

# Test the binary
echo "Testing upscayl-ncnn binary..."
cd src-tauri/binaries
if ./upscayl-ncnn-${TARGET_ARCH} 2>&1 | grep -q "Usage:"; then
    echo "✅ upscayl-ncnn binary is working correctly"
else
    echo "❌ upscayl-ncnn binary test failed"
    exit 1
fi
cd ../..

echo ""
echo "🎉 Upscayl integration setup complete!"
echo ""
echo "Available AI models:"
echo "  - realesrgan-x4plus (General purpose, 4x upscaling)"
echo "  - realesrgan-x4plus-anime (Optimized for anime/cartoon images)"
echo "  - realesr-animevideov3-x2 (Anime video, 2x upscaling)"
echo "  - realesr-animevideov3-x3 (Anime video, 3x upscaling)"
echo "  - realesr-animevideov3-x4 (Anime video, 4x upscaling)"
echo ""
echo "To test the integration:"
echo "  1. Run: npm run tauri dev"
echo "  2. Upload an image"
echo "  3. Click 'AI Upscaling' button"
echo ""
echo "Note: Upscaling requires a Vulkan-compatible GPU"
