# AI Image Upscaling Integration

This document explains how the Upscayl AI upscaling functionality has been integrated into your image processing application.

## Overview

The integration uses the Real-ESRGAN-ncnn-vulkan backend (the same engine that powers Upscayl) as a sidecar binary with your Tauri application. This provides high-quality AI-powered image upscaling directly within your app.

## Features

- **4x AI Upscaling**: Enhance image resolution by 4x using advanced AI models
- **Multiple Models**: Support for different AI models optimized for various image types
- **Real-time Processing**: Direct integration with your existing image processing workflow
- **Native Performance**: Uses the same high-performance NCNN Vulkan backend as Upscayl

## Available Models

1. **realesrgan-x4plus** (Default)
   - General purpose model for real-world images
   - Best for photographs and natural images
   - 4x upscaling

2. **realesrgan-x4plus-anime**
   - Optimized for anime and cartoon-style images
   - Better results for illustrations and artwork
   - 4x upscaling

3. **realesr-animevideov3-x2/x3/x4**
   - Specialized for anime video frames
   - Available in 2x, 3x, and 4x variants

## Setup

1. **Run the setup script**:
   ```bash
   ./setup-upscaling.sh
   ```

2. **Manual setup** (if script fails):
   ```bash
   # Create binaries directory
   mkdir -p src-tauri/binaries
   cd src-tauri/binaries
   
   # Download and extract binary
   curl -L -o realesrgan-ncnn-vulkan-macos.zip "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.5.0/realesrgan-ncnn-vulkan-20220424-macos.zip"
   unzip realesrgan-ncnn-vulkan-macos.zip
   mv realesrgan-ncnn-vulkan upscayl-ncnn
   chmod +x upscayl-ncnn
   ```

## System Requirements

- **GPU**: Vulkan-compatible graphics card
- **Memory**: At least 4GB RAM (8GB+ recommended for large images)
- **Storage**: ~100MB for models and binary

## Usage

1. **Start the application**:
   ```bash
   npm run tauri dev
   ```

2. **Upload an image** using the upload button

3. **Click "AI Upscaling"** in the processing tools section

4. **Wait for processing** (typically 1-3 minutes depending on image size)

5. **Download the result** using the download button

## Technical Implementation

### Backend (Rust)
- **Command**: `upscale_image` in `src-tauri/src/lib.rs`
- **Sidecar**: Uses Tauri's sidecar functionality to run upscayl-ncnn
- **Models**: Located in `src-tauri/binaries/models/`

### Frontend (React)
- **Handler**: `handleUpscaling` in `src/components/ImageProcessingStudio.tsx`
- **UI**: Interactive button with loading states and progress feedback

### Configuration
- **Tauri Config**: `src-tauri/tauri.conf.json` includes sidecar binary
- **Bundle**: Binary and models are bundled with the application

## Troubleshooting

### "Failed to get upscayl-ncnn sidecar"
- Ensure the binary exists in `src-tauri/binaries/upscayl-ncnn`
- Check that the binary has execute permissions: `chmod +x src-tauri/binaries/upscayl-ncnn`

### "Vulkan not supported"
- Your GPU may not support Vulkan
- Try updating your graphics drivers
- Check if your system has Vulkan support: `vulkaninfo` (if available)

### "Out of memory" errors
- Try with smaller images (< 2000x2000 pixels)
- Close other applications to free up GPU memory
- Consider using a different model or scale factor

### Slow processing
- Processing time depends on image size and GPU performance
- Larger images take significantly longer
- Consider resizing very large images before upscaling

## Performance Tips

1. **Optimal image sizes**: 500x500 to 1500x1500 pixels work best
2. **GPU memory**: Ensure sufficient VRAM (2GB+ recommended)
3. **Image format**: PNG and JPEG are fully supported
4. **Batch processing**: Process images one at a time for best results

## Integration with Existing Features

The upscaling feature integrates seamlessly with your existing image processing pipeline:

- **Background Removal + Upscaling**: Remove background first, then upscale
- **Face Restoration + Upscaling**: Restore faces, then enhance resolution
- **Download**: Supports the same native save dialog as other features

## Future Enhancements

Potential improvements for the upscaling feature:

1. **Model Selection**: UI to choose between different AI models
2. **Scale Factor Options**: Support for 2x, 3x, and 4x scaling
3. **Batch Processing**: Upscale multiple images at once
4. **Preview Mode**: Quick preview before full processing
5. **Custom Models**: Support for user-provided model files
